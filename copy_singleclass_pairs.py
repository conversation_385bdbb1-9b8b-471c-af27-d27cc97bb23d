#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sélectionne aléatoirement 36 000 paires négatives (background-only) et les copie
dans un nouveau dataset.
"""

import os
import cv2
import sys
import json
import time
import shutil
import random
import numpy as np
from pathlib import Path
from datetime import datetime

# ==== PARAMÈTRES HARDCODÉS ====
MASKS_FOLDER = Path(r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset035_TPI_calibprod_complet\labelsTr")
IMAGES_FOLDER = Path(r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset035_TPI_calibprod_complet\imagesTr")
OUT_MASKS_FOLDER = Path(r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset038_TPI_calibprod_complet_defaut_et_36k_images\labelsTr")
OUT_IMAGES_FOLDER = Path(r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset038_TPI_calibprod_complet_defaut_et_36k_images\imagesTr")

NUM_NEGATIVES = 36000
SEED = 42
DRY_RUN = False  # Mettre True pour tester sans copier

IMAGE_EXTS = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']


def find_corresponding_image(mask_path: Path, images_folder: Path) -> Path | None:
    stem = mask_path.stem
    for ext in IMAGE_EXTS:
        p = images_folder / f"{stem}{ext}"
        if p.exists():
            return p
    for ext in IMAGE_EXTS:
        p = images_folder / f"{stem}_0000{ext}"
        if p.exists():
            return p
    if stem.endswith('_0000'):
        base = stem[:-5]
        for ext in IMAGE_EXTS:
            p = images_folder / f"{base}{ext}"
            if p.exists():
                return p
    return None


def mask_is_negative(mask_path: Path) -> bool:
    m = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
    if m is None:
        raise RuntimeError(f"Impossible de lire le masque: {mask_path}")
    return int(m.max()) == 0


def copy_pair(mask_path: Path, image_path: Path, out_masks: Path, out_images: Path):
    out_masks.mkdir(parents=True, exist_ok=True)
    out_images.mkdir(parents=True, exist_ok=True)
    shutil.copy2(mask_path, out_masks / mask_path.name)
    shutil.copy2(image_path, out_images / image_path.name)


def main():
    if not MASKS_FOLDER.exists() or not IMAGES_FOLDER.exists():
        print("❌ Dossiers d'entrée introuvables.")
        sys.exit(1)

    mask_files = sorted(MASKS_FOLDER.glob("*.png"))
    if not mask_files:
        print(f"❌ Aucun masque PNG trouvé dans {MASKS_FOLDER}")
        sys.exit(1)

    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    OUT_MASKS_FOLDER.parent.mkdir(parents=True, exist_ok=True)
    OUT_IMAGES_FOLDER.parent.mkdir(parents=True, exist_ok=True)
    log_file = OUT_MASKS_FOLDER.parent / f"sample_negative_{ts}.txt"

    rng = random.Random(SEED)

    negatives = []
    start = time.time()

    for i, m in enumerate(mask_files, 1):
        try:
            if mask_is_negative(m):
                img = find_corresponding_image(m, IMAGES_FOLDER)
                if img:
                    negatives.append((m, img))
        except Exception:
            pass
        if i % 5000 == 0 or i == len(mask_files):
            print(f"▶ Scanné {i}/{len(mask_files)} - négatifs valides: {len(negatives)}")

    available = len(negatives)
    if NUM_NEGATIVES > available:
        print(f"⚠️ Seulement {available} négatifs disponibles, ajustement...")
        num_to_copy = available
    else:
        num_to_copy = NUM_NEGATIVES

    rng.shuffle(negatives)
    selection = negatives[:num_to_copy]

    copied = 0
    with open(log_file, "w", encoding="utf-8") as log:
        meta = {
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "requested": NUM_NEGATIVES,
            "selected": num_to_copy,
            "seed": SEED,
            "dry_run": DRY_RUN
        }
        log.write(json.dumps(meta, ensure_ascii=False, indent=2) + "\n")
        log.write("=" * 70 + "\n")

        if not DRY_RUN:
            print(f"🚚 Copie de {num_to_copy} paires négatives...")
            for k, (m, img) in enumerate(selection, 1):
                copy_pair(m, img, OUT_MASKS_FOLDER, OUT_IMAGES_FOLDER)
                copied += 1
                if k % 2000 == 0 or k == num_to_copy:
                    print(f"  ▶ {k}/{num_to_copy} copiées")
                log.write(f"{m.name} | {img.name}\n")
        else:
            print(f"[DRY RUN] {num_to_copy} fichiers seraient copiés.")
            for m, img in selection[:20]:
                log.write(f"[DRY] {m.name} | {img.name}\n")

    print("\n✅ Terminé.")
    print(f"   Disponibles : {available}")
    print(f"   Copiés      : {copied if not DRY_RUN else 0}")
    print(f"   Journal     : {log_file}")


if __name__ == "__main__":
    main()
