#!/usr/bin/env python3
"""
Script pour créer des splits équilibrés pour nnUNet
en analysant la distribution des classes dans le dataset.

exemple de commande:
python create_balanced_splits.py C:\Users\<USER>\Documents\4Corrosit\nnUNet_raw\Dataset035_TPI_calibprod_complet --output splits_final.json --folds 5

"""

import json
import numpy as np
from PIL import Image
from pathlib import Path
from sklearn.model_selection import StratifiedKFold
import argparse


def analyze_dataset(dataset_path):
    """Analyser la distribution des classes dans le dataset"""
    labels_dir = Path(dataset_path) / "labelsTr"

    if not labels_dir.exists():
        print(f"❌ Dossier {labels_dir} non trouvé")
        return None

    print("🔍 Analyse du dataset...")

    image_stats = []

    # Chercher d'abord les fichiers .nii.gz, puis .png
    label_files = list(labels_dir.glob("*.nii.gz"))
    if not label_files:
        label_files = list(labels_dir.glob("*.png"))
        file_type = "PNG"
    else:
        file_type = "NIfTI"

    print(f"📁 Type de fichiers détecté: {file_type}")
    print(f"📊 Nombre de fichiers trouvés: {len(label_files)}")

    for label_file in sorted(label_files):
        try:
            if file_type == "PNG":
                # Charger l'image PNG
                img = Image.open(label_file)
                data = np.array(img)
            else:
                # Charger le fichier NIfTI
                import nibabel as nib
                img = nib.load(label_file)
                data = img.get_fdata()

            # Calculer les statistiques
            unique_values, counts = np.unique(data, return_counts=True)
            total_pixels = data.size

            # Calculer le pourcentage de foreground (classe > 0)
            foreground_pixels = np.sum(counts[unique_values > 0]) if len(unique_values) > 1 else 0
            foreground_percentage = (foreground_pixels / total_pixels) * 100

            # Extraire l'ID du cas
            if file_type == "PNG":
                case_id = label_file.stem  # Nom sans extension
            else:
                case_id = label_file.stem.replace('.nii', '')

            image_stats.append({
                'filename': label_file.name,
                'case_id': case_id,
                'total_pixels': total_pixels,
                'foreground_pixels': foreground_pixels,
                'foreground_percentage': foreground_percentage,
                'has_foreground': foreground_pixels > 0
            })

        except Exception as e:
            print(f"⚠️ Erreur avec {label_file}: {e}")

    return image_stats


def create_stratified_splits(image_stats, n_splits=5, random_state=42):
    """Créer des splits stratifiés basés sur la présence de foreground"""
    
    # Créer les catégories pour la stratification
    categories = []
    case_ids = []
    
    for stat in image_stats:
        case_ids.append(stat['case_id'])
        
        # Catégoriser basé sur le pourcentage de foreground
        if stat['foreground_percentage'] == 0:
            categories.append(0)  # Pas de foreground
        elif stat['foreground_percentage'] < 1:
            categories.append(1)  # Peu de foreground
        elif stat['foreground_percentage'] < 5:
            categories.append(2)  # Foreground modéré
        else:
            categories.append(3)  # Beaucoup de foreground
    
    print(f"📊 Distribution des catégories:")
    unique_cats, cat_counts = np.unique(categories, return_counts=True)
    for cat, count in zip(unique_cats, cat_counts):
        cat_names = {0: "Pas de foreground", 1: "Peu de foreground", 
                    2: "Foreground modéré", 3: "Beaucoup de foreground"}
        print(f"   {cat_names.get(cat, f'Catégorie {cat}')}: {count} images")
    
    # Créer les splits stratifiés
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    
    splits = []
    for fold, (train_idx, val_idx) in enumerate(skf.split(case_ids, categories)):
        train_cases = [case_ids[i] for i in train_idx]
        val_cases = [case_ids[i] for i in val_idx]
        
        # Vérifier la distribution dans ce fold
        train_cats = [categories[i] for i in train_idx]
        val_cats = [categories[i] for i in val_idx]
        
        train_fg = sum(1 for cat in train_cats if cat > 0)
        val_fg = sum(1 for cat in val_cats if cat > 0)
        
        print(f"📁 Fold {fold}:")
        print(f"   Train: {len(train_cases)} images ({train_fg} avec foreground)")
        print(f"   Val:   {len(val_cases)} images ({val_fg} avec foreground)")
        
        splits.append({
            'train': train_cases,
            'val': val_cases
        })
    
    return splits


def save_splits_file(splits, output_path):
    """Sauvegarder le fichier splits_final.json pour nnUNet"""
    
    # Format attendu par nnUNet
    splits_data = []
    for i, split in enumerate(splits):
        splits_data.append({
            'train': split['train'],
            'val': split['val']
        })
    
    with open(output_path, 'w') as f:
        json.dump(splits_data, f, indent=2)
    
    print(f"✅ Splits sauvegardés dans {output_path}")


def main():
    parser = argparse.ArgumentParser(description='Créer des splits équilibrés pour nnUNet')
    parser.add_argument('dataset_path', help='Chemin vers le dataset nnUNet')
    parser.add_argument('--output', '-o', default='splits_final.json', 
                       help='Fichier de sortie (défaut: splits_final.json)')
    parser.add_argument('--folds', '-f', type=int, default=5, 
                       help='Nombre de folds (défaut: 5)')
    
    args = parser.parse_args()
    
    # Analyser le dataset
    image_stats = analyze_dataset(args.dataset_path)
    if image_stats is None:
        return
    
    print(f"\n📈 Statistiques globales:")
    total_images = len(image_stats)
    images_with_fg = sum(1 for stat in image_stats if stat['has_foreground'])
    avg_fg_percentage = np.mean([stat['foreground_percentage'] for stat in image_stats])
    
    print(f"   Total d'images: {total_images}")
    print(f"   Images avec foreground: {images_with_fg} ({images_with_fg/total_images*100:.1f}%)")
    print(f"   Pourcentage moyen de foreground: {avg_fg_percentage:.3f}%")
    
    # Créer les splits
    print(f"\n🔄 Création de {args.folds} splits stratifiés...")
    splits = create_stratified_splits(image_stats, n_splits=args.folds)
    
    # Sauvegarder
    save_splits_file(splits, args.output)
    
    print(f"\n🎯 Pour utiliser ces splits avec nnUNet:")
    print(f"   1. Copiez {args.output} dans votre dossier de dataset")
    print(f"   2. Relancez l'entraînement normalement")


if __name__ == "__main__":
    main()
