"""
Script pour copier seulement les images avec masques multi-classes
Vérifie les classes dans les masques et copie seulement les images qui ont plus d'une classe
(plus que juste le background [0])

Auteur: Gabriel Forest
Date: 2025-08-13
"""

import os
import cv2
import numpy as np
import shutil
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from datetime import datetime

class MultiClassImageCopier:
    def __init__(self):
        """Initialise le copieur d'images multi-classes"""
        self.input_folder = ""
        self.output_folder = ""
        self.processed_count = 0
        self.copied_count = 0
        self.skipped_count = 0
        self.total_count = 0
        self.progress_callback = None
        
    def analyze_mask_classes(self, mask_path):
        """
        Analyse les classes présentes dans un masque
        
        Args:
            mask_path (str): Chemin vers le masque
            
        Returns:
            tuple: (unique_values, has_multiple_classes)
        """
        try:
            # Charger le masque en niveaux de gris
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                return None, False
                
            # Trouver les valeurs uniques
            unique_values = np.unique(mask)
            
            # Vérifier s'il y a plus d'une classe (plus que juste le background 0)
            non_background_classes = unique_values[unique_values != 0]
            has_multiple_classes = len(non_background_classes) > 0
            
            return unique_values.tolist(), has_multiple_classes
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse du masque {mask_path}: {e}")
            return None, False
    
    def find_corresponding_image(self, mask_path, images_folder):
        """
        Trouve l'image correspondante au masque
        
        Args:
            mask_path (Path): Chemin vers le masque
            images_folder (Path): Dossier des images
            
        Returns:
            Path or None: Chemin vers l'image correspondante
        """
        mask_name = mask_path.stem  # nom sans extension
        
        # Extensions d'images supportées
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
        
        for ext in image_extensions:
            image_path = images_folder / f"{mask_name}{ext}"
            if image_path.exists():
                return image_path
                
        return None
    
    def copy_file_simple(self, src_path, dest_folder):
        """
        Copie un fichier directement dans le dossier de destination (sans structure)

        Args:
            src_path (Path): Chemin source
            dest_folder (Path): Dossier de destination
        """
        dest_path = dest_folder / src_path.name

        # Copier le fichier
        shutil.copy2(src_path, dest_path)

        return dest_path
    
    def process_folder(self, input_folder, output_folder, progress_callback=None):
        """
        Traite un dossier et copie les images avec masques multi-classes
        
        Args:
            input_folder (str): Dossier d'entrée
            output_folder (str): Dossier de sortie
            progress_callback (function): Fonction de callback pour le progrès
        """
        self.progress_callback = progress_callback
        self.processed_count = 0
        self.copied_count = 0
        self.skipped_count = 0
        
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        
        # Créer le dossier de sortie (un seul dossier, pas de sous-dossiers)
        output_path.mkdir(parents=True, exist_ok=True)

        # Chercher tous les fichiers PNG directement dans le dossier d'entrée (pas de récursion)
        mask_files = list(input_path.glob("*.png"))

        # Tous les PNG sont considérés comme des masques potentiels
        filtered_masks = mask_files
        
        self.total_count = len(filtered_masks)
        
        if self.total_count == 0:
            raise ValueError("Aucun fichier de masque trouvé dans le dossier d'entrée")
        
        print(f"📁 {self.total_count} masques trouvés")
        
        # Créer un fichier de log
        log_file = output_path / f"copy_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(log_file, 'w', encoding='utf-8') as log:
            log.write(f"Copie d'images multi-classes - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            log.write(f"Dossier source: {input_folder}\n")
            log.write(f"Dossier destination: {output_folder}\n")
            log.write(f"Nombre total de masques: {self.total_count}\n")
            log.write("=" * 70 + "\n\n")
            
            for i, mask_file in enumerate(filtered_masks, 1):
                self.processed_count = i
                
                # Analyser les classes du masque
                unique_values, has_multiple_classes = self.analyze_mask_classes(mask_file)
                
                if unique_values is None:
                    self.skipped_count += 1
                    log.write(f"❌ ERREUR - {mask_file.name}: Impossible de lire le masque\n")
                    continue
                
                log.write(f"📋 {mask_file.name}: classes {unique_values}")
                
                if has_multiple_classes:
                    # Chercher l'image correspondante dans le même dossier
                    image_file = self.find_corresponding_image(mask_file, input_path)

                    if image_file and image_file.exists():
                        try:
                            # Copier directement l'image dans le dossier de sortie
                            copied_image = self.copy_file_simple(image_file, output_path)

                            self.copied_count += 1
                            log.write(f" → ✅ COPIÉ\n")
                            log.write(f"   Image: {copied_image}\n")

                        except Exception as e:
                            self.skipped_count += 1
                            log.write(f" → ❌ ERREUR lors de la copie: {e}\n")
                    else:
                        self.skipped_count += 1
                        log.write(f" → ⚠️ IGNORÉ: Image correspondante non trouvée\n")
                else:
                    self.skipped_count += 1
                    log.write(f" → ⏭️ IGNORÉ: Une seule classe (background uniquement)\n")
                
                # Callback de progrès
                if self.progress_callback:
                    progress = (i / self.total_count) * 100
                    self.progress_callback(progress, f"Traitement: {i}/{self.total_count}")
                
                # Affichage périodique
                if i % 100 == 0 or i == self.total_count:
                    print(f"📊 [{i:6d}/{self.total_count:6d}] - Copiés: {self.copied_count}, Ignorés: {self.skipped_count}")
            
            # Résumé final dans le log
            log.write("\n" + "=" * 70 + "\n")
            log.write("RÉSUMÉ FINAL\n")
            log.write("=" * 70 + "\n")
            log.write(f"Masques traités: {self.processed_count}\n")
            log.write(f"Images copiées: {self.copied_count}\n")
            log.write(f"Images ignorées: {self.skipped_count}\n")
            log.write(f"Taux de copie: {(self.copied_count/self.processed_count)*100:.1f}%\n")
        
        print(f"\n✅ Traitement terminé!")
        print(f"📋 Masques traités: {self.processed_count}")
        print(f"✅ Images copiées: {self.copied_count}")
        print(f"⏭️ Images ignorées: {self.skipped_count}")
        print(f"📄 Log sauvegardé: {log_file}")

class MultiClassImageCopierGUI:
    def __init__(self):
        """Initialise l'interface graphique"""
        self.root = tk.Tk()
        self.root.title("Copieur d'Images Multi-Classes")
        self.root.geometry("600x400")
        
        self.copier = MultiClassImageCopier()
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Copieur d'Images Multi-Classes", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Description
        desc_text = ("Ce script copie seulement les images dont les masques contiennent\n"
                    "plus d'une classe (plus que juste le background [0]).")
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.CENTER)
        desc_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # Sélection du dossier d'entrée
        ttk.Label(main_frame, text="Dossier d'entrée:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.input_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.input_var, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(main_frame, text="Parcourir", command=self.select_input_folder).grid(row=2, column=2, padx=5)
        
        # Sélection du dossier de sortie
        ttk.Label(main_frame, text="Dossier de sortie:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.output_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.output_var, width=50).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(main_frame, text="Parcourir", command=self.select_output_folder).grid(row=3, column=2, padx=5)
        
        # Barre de progression
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        # Label de statut
        self.status_var = tk.StringVar(value="Prêt")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5)
        
        # Bouton de traitement
        self.process_button = ttk.Button(main_frame, text="Démarrer le traitement", 
                                       command=self.start_processing)
        self.process_button.grid(row=6, column=0, columnspan=3, pady=20)
        
    def select_input_folder(self):
        """Sélectionne le dossier d'entrée"""
        folder = filedialog.askdirectory(title="Sélectionner le dossier d'entrée")
        if folder:
            self.input_var.set(folder)
            
    def select_output_folder(self):
        """Sélectionne le dossier de sortie"""
        folder = filedialog.askdirectory(title="Sélectionner le dossier de sortie")
        if folder:
            self.output_var.set(folder)
            
    def progress_callback(self, progress, status):
        """Callback pour mettre à jour la progression"""
        self.root.after(0, lambda: self.progress_var.set(progress))
        self.root.after(0, lambda: self.status_var.set(status))
        
    def start_processing(self):
        """Démarre le traitement"""
        input_folder = self.input_var.get().strip()
        output_folder = self.output_var.get().strip()
        
        if not input_folder or not output_folder:
            messagebox.showerror("Erreur", "Veuillez sélectionner les dossiers d'entrée et de sortie")
            return
            
        if not os.path.exists(input_folder):
            messagebox.showerror("Erreur", "Le dossier d'entrée n'existe pas")
            return
            
        # Désactiver le bouton pendant le traitement
        self.process_button.config(state='disabled')
        self.progress_var.set(0)
        self.status_var.set("Démarrage du traitement...")
        
        # Lancer le traitement dans un thread séparé
        thread = threading.Thread(target=self.process_images, 
                                 args=(input_folder, output_folder))
        thread.daemon = True
        thread.start()
        
    def process_images(self, input_folder, output_folder):
        """Traite les images (exécuté dans un thread séparé)"""
        try:
            self.copier.process_folder(input_folder, output_folder, self.progress_callback)
            
            # Réactiver le bouton et afficher le succès
            self.root.after(0, lambda: self.process_button.config(state='normal'))
            self.root.after(0, lambda: messagebox.showinfo("Succès", 
                           f"Traitement terminé!\n"
                           f"Images copiées: {self.copier.copied_count}\n"
                           f"Images ignorées: {self.copier.skipped_count}"))
            
        except Exception as e:
            self.root.after(0, lambda: self.process_button.config(state='normal'))
            self.root.after(0, lambda: messagebox.showerror("Erreur", f"Erreur: {e}"))
            
    def run(self):
        """Lance l'interface graphique"""
        self.root.mainloop()

if __name__ == "__main__":
    import sys

    # === CHEMINS HARDCODÉS ===
    INPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference\MX2-757-SC-PS-00575-Z-13000_IA_Training_rotate_de_90_v3"
    OUTPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference\MX2-757-SC-PS-00575-Z-13000_IA_Training_rotate_de_90_v3\Nouveau dossier"

    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        # Mode interface graphique
        app = MultiClassImageCopierGUI()
        app.run()
    else:
        # Mode ligne de commande avec chemins hardcodés
        print("🎯 COPIEUR D'IMAGES MULTI-CLASSES")
        print("=" * 50)
        print("Ce script copie seulement les images dont les masques")
        print("contiennent plus d'une classe (plus que juste le background [0])")
        print("=" * 50)
        print(f"📂 Dossier d'entrée: {INPUT_FOLDER}")
        print(f"📤 Dossier de sortie: {OUTPUT_FOLDER}")
        print("=" * 50)

        # Vérifier que le dossier d'entrée existe
        if not os.path.exists(INPUT_FOLDER):
            print(f"❌ Dossier d'entrée introuvable: {INPUT_FOLDER}")
            print("💡 Modifiez la variable INPUT_FOLDER dans le script")
            sys.exit(1)

        # Confirmation avant traitement
        response = input("Continuer le traitement? (o/N): ")
        if response.lower() not in ['o', 'oui', 'y', 'yes']:
            print("Traitement annulé")
            sys.exit(0)

        # Traitement
        copier = MultiClassImageCopier()
        try:
            copier.process_folder(INPUT_FOLDER, OUTPUT_FOLDER)
        except Exception as e:
            print(f"❌ Erreur: {e}")
            sys.exit(1)
